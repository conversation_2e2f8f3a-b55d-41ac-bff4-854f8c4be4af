using System;
using System.ComponentModel;
using System.Windows.Forms;

namespace TreeviewDemo
{
    /// <summary>
    /// 拖拽模式枚举
    /// </summary>
    public enum DragDropMode
    {
        /// <summary>
        /// 任意层级下可互相拖拽
        /// </summary>
        [Description("任意层级下可互相拖拽")]
        AnyLevel,
        /// <summary>
        /// 同一层级下可互相拖拽
        /// </summary>
        [Description("同一层级下可互相拖拽")]
        SameLevel,
        /// <summary>
        /// 同一层级且同一父节点下可互相拖拽
        /// </summary>
        [Description("同一层级且同一父节点下可互相拖拽")]
        SameParentAndLevel
    }

    /// <summary>
    /// FpiTreeView：直接继承标准TreeView的N级拖拽排序自定义控件。
    /// 支持节点链路提示、同级拖拽排序、详细注释等功能。
    /// </summary>
    public class FpiTreeView : TreeView
    {
        /// <summary>
        /// 拖拽提示用ToolTip控件。
        /// </summary>
        private ToolTip toolTip1 = new ToolTip();
        /// <summary>
        /// 当前正在拖拽的节点链路文本。
        /// </summary>
        private string draggingNodeText = null;
        /// <summary>
        /// 拖拽状态标记。
        /// </summary>
        private bool isDragging = false;
        /// <summary>
        /// 上一次ToolTip显示的鼠标点。
        /// </summary>
        private System.Drawing.Point lastToolTipPoint = System.Drawing.Point.Empty;
        /// <summary>
        /// 拖拽模式，支持三种：任意层级、同层级、同层级同父节点
        /// </summary>
        public DragDropMode DragDropMode { get; set; } = DragDropMode.SameParentAndLevel;

        /// <summary>
        /// 构造函数，初始化控件及事件绑定。
        /// </summary>
        public FpiTreeView()
        {
            this.AllowDrop = true;
            this.Dock = DockStyle.Fill;
            toolTip1.IsBalloon = true;
            // 绑定拖拽相关事件
            this.ItemDrag += FpiTreeView_ItemDrag;
            this.DragEnter += FpiTreeView_DragEnter;
            this.DragDrop += FpiTreeView_DragDrop;
            this.DragOver += FpiTreeView_DragOver;
            this.DragLeave += FpiTreeView_DragLeave;
        }

        /// <summary>
        /// 获取指定节点的完整父链路（如"一级/二级/三级"）。
        /// </summary>
        /// <param name="node">目标节点</param>
        /// <returns>链路字符串</returns>
        private string GetNodePath(TreeNode node)
        {
            if(node == null) return string.Empty;
            string path = node.Text;
            TreeNode parent = node.Parent;
            while(parent != null)
            {
                path = parent.Text + "/" + path;
                parent = parent.Parent;
            }
            return path;
        }

        /// <summary>
        /// 拖拽开始事件，记录拖拽节点链路并启动拖拽。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void FpiTreeView_ItemDrag(object sender, ItemDragEventArgs e)
        {
            if(e.Item is TreeNode node)
            {
                draggingNodeText = GetNodePath(node);
                isDragging = true;
            }
            this.DoDragDrop(e.Item, DragDropEffects.Move);
            draggingNodeText = null;
            isDragging = false;
            toolTip1.Hide(this);
        }

        /// <summary>
        /// 拖拽进入控件时设置拖拽效果。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void FpiTreeView_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.Move;
        }

        /// <summary>
        /// 拖拽过程中显示链路提示，仅在允许拖拽区域显示。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void FpiTreeView_DragOver(object sender, DragEventArgs e)
        {
            if(!isDragging || string.IsNullOrEmpty(draggingNodeText))
            {
                toolTip1.Hide(this);
                e.Effect = DragDropEffects.None;
                return;
            }
            var pt = this.PointToClient(new System.Drawing.Point(e.X, e.Y));
            var targetNode = this.GetNodeAt(pt);
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            bool canDrop = false;
            // 拖拽模式判断
            if(draggedNode != null && targetNode != null && draggedNode != targetNode)
            {
                switch(DragDropMode)
                {
                    case DragDropMode.AnyLevel:
                        canDrop = true;
                        break;
                    case DragDropMode.SameLevel:
                        canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                        break;
                    case DragDropMode.SameParentAndLevel:
                        canDrop = draggedNode.Parent == targetNode.Parent;
                        break;
                }
            }
            if(canDrop)
            {
                e.Effect = DragDropEffects.Move;
                if(lastToolTipPoint != pt)
                {
                    toolTip1.Show($"正在拖拽：{draggingNodeText}", this, pt.X + 20, pt.Y + 20, int.MaxValue);
                    lastToolTipPoint = pt;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
                toolTip1.Hide(this);
                lastToolTipPoint = System.Drawing.Point.Empty;
            }
        }

        /// <summary>
        /// 拖拽释放时处理节点排序和父节点限制，支持N级节点和多种拖拽模式。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void FpiTreeView_DragDrop(object sender, DragEventArgs e)
        {
            toolTip1.Hide(this);
            isDragging = false;
            draggingNodeText = null;
            lastToolTipPoint = System.Drawing.Point.Empty;
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            if(draggedNode == null) return;
            var pt = this.PointToClient(new System.Drawing.Point(e.X, e.Y));
            var targetNode = this.GetNodeAt(pt);
            if(targetNode == null || draggedNode == targetNode) return;
            // 拖拽模式判断
            bool canDrop = false;
            switch(DragDropMode)
            {
                case DragDropMode.AnyLevel:
                    canDrop = true;
                    break;
                case DragDropMode.SameLevel:
                    canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                    break;
                case DragDropMode.SameParentAndLevel:
                    canDrop = draggedNode.Parent == targetNode.Parent;
                    break;
            }
            if(canDrop)
            {
                if(DragDropMode == DragDropMode.AnyLevel || DragDropMode == DragDropMode.SameLevel)
                {
                    // 插入到目标节点父节点下，排在目标节点之前
                    TreeNodeCollection collection;
                    if(targetNode.Parent == null)
                        collection = this.Nodes;
                    else
                        collection = targetNode.Parent.Nodes;
                    if(draggedNode.Parent == null)
                        this.Nodes.Remove(draggedNode);
                    else
                        draggedNode.Parent.Nodes.Remove(draggedNode);
                    int targetIndex = targetNode.Index;
                    collection.Insert(targetIndex, draggedNode);
                    this.SelectedNode = draggedNode;
                }
                else if(DragDropMode == DragDropMode.SameParentAndLevel)
                {
                    // 只允许同一父节点下排序
                    if(draggedNode.Parent == null)
                    {
                        int targetIndex = targetNode.Index;
                        this.Nodes.Remove(draggedNode);
                        this.Nodes.Insert(targetIndex, draggedNode);
                        this.SelectedNode = draggedNode;
                    }
                    else
                    {
                        var parent = draggedNode.Parent;
                        int targetIndex = targetNode.Index;
                        parent.Nodes.Remove(draggedNode);
                        parent.Nodes.Insert(targetIndex, draggedNode);
                        this.SelectedNode = draggedNode;
                    }
                }
            }
        }

        /// <summary>
        /// 拖拽离开控件时隐藏ToolTip并重置状态。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void FpiTreeView_DragLeave(object sender, EventArgs e)
        {
            toolTip1.Hide(this);
            isDragging = false;
            draggingNodeText = null;
            lastToolTipPoint = System.Drawing.Point.Empty;
        }

        /// <summary>
        /// 获取节点的层级（根节点为0）
        /// </summary>
        /// <param name="node">目标节点</param>
        /// <returns>层级数</returns>
        private int GetNodeLevel(TreeNode node)
        {
            int level = 0;
            while(node.Parent != null)
            {
                level++;
                node = node.Parent;
            }
            return level;
        }
    }
}