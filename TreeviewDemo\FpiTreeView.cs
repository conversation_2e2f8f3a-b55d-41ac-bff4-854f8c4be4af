using System;
using System.ComponentModel;
using System.Windows.Forms;

namespace TreeviewDemo
{
    /// <summary>
    /// 拖拽模式枚举
    /// </summary>
    public enum DragDropMode
    {
        /// <summary>
        /// 任意层级下可互相拖拽
        /// </summary>
        [Description("任意层级下可互相拖拽")]
        AnyLevel,
        /// <summary>
        /// 同一层级下可互相拖拽
        /// </summary>
        [Description("同一层级下可互相拖拽")]
        SameLevel,
        /// <summary>
        /// 同一层级且同一父节点下可互相拖拽
        /// </summary>
        [Description("同一层级且同一父节点下可互相拖拽")]
        SameParentAndLevel
    }

    /// <summary>
    /// FpiTreeView：直接继承标准TreeView的N级拖拽排序自定义控件。
    /// 支持节点链路提示、同级拖拽排序、详细注释等功能。
    /// </summary>
    public class FpiTreeView : TreeView
    {
        /// <summary>
        /// 拖拽提示用ToolTip控件。
        /// </summary>
        private ToolTip toolTip1 = new ToolTip();
        /// <summary>
        /// 当前正在拖拽的节点链路文本。
        /// </summary>
        private string draggingNodeText = null;
        /// <summary>
        /// 拖拽状态标记。
        /// </summary>
        private bool isDragging = false;
        /// <summary>
        /// 上一次ToolTip显示的鼠标点。
        /// </summary>
        private System.Drawing.Point lastToolTipPoint = System.Drawing.Point.Empty;
        /// <summary>
        /// 拖拽模式，支持三种：任意层级、同层级、同层级同父节点
        /// </summary>
        public DragDropMode DragDropMode { get; set; } = DragDropMode.SameParentAndLevel;

        /// <summary>
        /// 是否允许跨TreeView控件拖拽数据。
        /// 该属性用于控制是否允许在不同的TreeView控件之间进行拖拽操作。
        ///
        /// 生效条件：
        /// - 仅在拖拽模式设置为 DragDropMode.AnyLevel 时生效
        /// - 参与跨控件拖拽的所有TreeView控件都必须启用此属性
        /// - 目标控件必须支持接收外部拖拽数据
        ///
        /// 使用场景：
        /// - 多个TreeView控件间的数据迁移
        /// - 不同树结构间的节点复制或移动
        /// - 复杂界面中的数据重组操作
        ///
        /// 注意事项：
        /// - 跨控件拖拽会忽略层级限制，允许任意层级间的拖拽
        /// - 拖拽完成后节点会从源控件中移除并添加到目标控件
        /// - 建议在启用此功能前确保数据结构的兼容性
        /// </summary>
        [Description("是否允许跨TreeView控件拖拽数据")]
        public bool AllowCrossControlDrag { get; set; } = false;

        /// <summary>
        /// 构造函数，初始化控件及事件绑定。
        /// </summary>
        public FpiTreeView()
        {
            this.AllowDrop = true;
            this.Dock = DockStyle.Fill;
            toolTip1.IsBalloon = true;
            // 绑定拖拽相关事件
            this.ItemDrag += FpiTreeView_ItemDrag;
            this.DragEnter += FpiTreeView_DragEnter;
            this.DragDrop += FpiTreeView_DragDrop;
            this.DragOver += FpiTreeView_DragOver;
            this.DragLeave += FpiTreeView_DragLeave;
        }

        /// <summary>
        /// 获取指定节点的完整父链路（如"一级/二级/三级"）。
        /// 该方法用于构建节点的完整路径字符串，便于在拖拽提示中显示节点的层级关系。
        /// 路径格式：从根节点到当前节点的完整路径，使用"/"分隔各级节点名称。
        /// </summary>
        /// <param name="node">目标节点，如果为null则返回空字符串</param>
        /// <returns>节点的完整路径字符串，如"根节点/子节点/孙节点"</returns>
        private string GetNodePath(TreeNode node)
        {
            if(node == null) return string.Empty;

            // 从当前节点开始构建路径
            string path = node.Text;
            TreeNode parent = node.Parent;

            // 向上遍历所有父节点，构建完整路径
            while(parent != null)
            {
                path = parent.Text + "/" + path;
                parent = parent.Parent;
            }
            return path;
        }

        /// <summary>
        /// 拖拽开始事件，记录拖拽节点链路并启动拖拽操作。
        /// 该事件在用户开始拖拽树节点时触发，负责：
        /// 1. 记录被拖拽节点的完整路径信息
        /// 2. 设置拖拽状态标记
        /// 3. 启动系统拖拽操作
        /// 4. 拖拽结束后清理状态和UI提示
        /// </summary>
        /// <param name="sender">事件源（FpiTreeView控件实例）</param>
        /// <param name="e">拖拽事件参数，包含被拖拽的项目信息</param>
        private void FpiTreeView_ItemDrag(object sender, ItemDragEventArgs e)
        {
            // 检查拖拽项是否为TreeNode，并记录其路径信息
            if(e.Item is TreeNode node)
            {
                draggingNodeText = GetNodePath(node); // 记录拖拽节点的完整路径
                isDragging = true; // 设置拖拽状态标记
            }

            // 启动系统拖拽操作，设置拖拽效果为移动
            this.DoDragDrop(e.Item, DragDropEffects.Move);

            // 拖拽操作结束后清理状态
            draggingNodeText = null;
            isDragging = false;
            toolTip1.Hide(this); // 隐藏可能残留的提示
        }

        /// <summary>
        /// 拖拽进入控件时设置拖拽效果。
        /// 该事件在拖拽对象首次进入控件边界时触发，用于设置拖拽操作的视觉效果。
        /// 支持单控件内拖拽和跨控件拖拽两种模式，具体的拖拽限制在DragOver事件中处理。
        /// </summary>
        /// <param name="sender">事件源（FpiTreeView控件实例）</param>
        /// <param name="e">拖拽事件参数</param>
        private void FpiTreeView_DragEnter(object sender, DragEventArgs e)
        {
            // 检查拖拽数据是否包含TreeNode
            if (e.Data.GetDataPresent(typeof(TreeNode)))
            {
                // 设置拖拽效果为移动，允许拖拽操作继续
                // 跨控件拖拽和单控件拖拽都使用相同的移动效果
                e.Effect = DragDropEffects.Move;
            }
            else
            {
                // 不支持的数据类型，禁止拖拽
                e.Effect = DragDropEffects.None;
            }
        }

        /// <summary>
        /// 拖拽过程中显示链路提示，仅在允许拖拽区域显示。
        /// 该方法在拖拽对象在控件上方移动时持续触发，用于：
        /// 1. 实时判断当前位置是否允许放置拖拽对象
        /// 2. 根据拖拽模式限制拖拽范围
        /// 3. 显示拖拽节点的完整路径提示
        /// 4. 设置合适的拖拽效果（允许/禁止）
        /// </summary>
        /// <param name="sender">事件源（FpiTreeView控件实例）</param>
        /// <param name="e">拖拽事件参数，包含鼠标位置和拖拽数据</param>
        private void FpiTreeView_DragOver(object sender, DragEventArgs e)
        {
            // 检查拖拽状态和节点文本是否有效，无效则禁止拖拽并隐藏提示
            if(!isDragging || string.IsNullOrEmpty(draggingNodeText))
            {
                toolTip1.Hide(this);
                e.Effect = DragDropEffects.None;
                return;
            }

            // 将屏幕坐标转换为控件内部坐标
            var pt = this.PointToClient(new System.Drawing.Point(e.X, e.Y));
            // 获取鼠标位置下的目标节点
            var targetNode = this.GetNodeAt(pt);
            // 从拖拽数据中获取被拖拽的节点
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            bool canDrop = false;

            // 拖拽模式判断：确保拖拽节点和目标节点都存在且不是同一个节点
            if(draggedNode != null && targetNode != null && draggedNode != targetNode)
            {
                // 检查是否为跨控件拖拽
                bool isCrossControlDrag = !this.Nodes.Contains(draggedNode) && !IsNodeInTree(draggedNode, this);

                if (isCrossControlDrag)
                {
                    // 跨控件拖拽：仅在启用跨控件拖拽且为任意层级模式时允许
                    canDrop = AllowCrossControlDrag && DragDropMode == DragDropMode.AnyLevel;
                }
                else
                {
                    // 单控件内拖拽：按原有逻辑处理
                    switch(DragDropMode)
                    {
                        case DragDropMode.AnyLevel:
                            // 任意层级模式：允许所有有效的拖拽操作
                            canDrop = true;
                            break;
                        case DragDropMode.SameLevel:
                            // 同层级模式：只允许相同层级的节点间拖拽
                            canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                            break;
                        case DragDropMode.SameParentAndLevel:
                            // 同父节点同层级模式：只允许相同父节点下的节点间拖拽
                            canDrop = draggedNode.Parent == targetNode.Parent;
                            break;
                    }
                }
            }

            if(canDrop)
            {
                // 允许拖拽：设置移动效果并显示路径提示
                e.Effect = DragDropEffects.Move;
                // 只有当鼠标位置发生变化时才更新ToolTip，避免频繁刷新
                if(lastToolTipPoint != pt)
                {
                    toolTip1.Show($"正在拖拽：{draggingNodeText}", this, pt.X + 20, pt.Y + 20, int.MaxValue);
                    lastToolTipPoint = pt;
                }
            }
            else
            {
                // 禁止拖拽：设置无效果并隐藏提示
                e.Effect = DragDropEffects.None;
                toolTip1.Hide(this);
                lastToolTipPoint = System.Drawing.Point.Empty;
            }
        }

        /// <summary>
        /// 拖拽释放时处理节点排序和父节点限制，支持N级节点和多种拖拽模式。
        /// 该方法是拖拽操作的最终处理阶段，负责：
        /// 1. 验证拖拽操作的有效性
        /// 2. 根据拖拽模式执行相应的节点重排序逻辑
        /// 3. 更新树结构并选中移动后的节点
        /// 4. 清理拖拽状态和UI提示
        /// </summary>
        /// <param name="sender">事件源（FpiTreeView控件实例）</param>
        /// <param name="e">拖拽事件参数，包含拖拽数据和鼠标位置信息</param>
        private void FpiTreeView_DragDrop(object sender, DragEventArgs e)
        {
            // 隐藏ToolTip（UI清理）
            toolTip1.Hide(this);

            // 获取被拖拽的节点
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            if(draggedNode == null)
            {
                // 拖拽数据无效，重置状态并返回
                ResetDragState();
                return;
            }

            // 获取目标位置的节点
            var pt = this.PointToClient(new System.Drawing.Point(e.X, e.Y));
            var targetNode = this.GetNodeAt(pt);
            if(targetNode == null || draggedNode == targetNode)
            {
                // 目标无效，重置状态并返回
                ResetDragState();
                return;
            }

            // 拖拽模式判断：再次验证是否允许此次拖拽操作
            bool canDrop = false;

            // 检查是否为跨控件拖拽
            bool isCrossControlDrag = !this.Nodes.Contains(draggedNode) && !IsNodeInTree(draggedNode, this);

            if (isCrossControlDrag)
            {
                // 跨控件拖拽：仅在启用跨控件拖拽且为任意层级模式时允许
                canDrop = AllowCrossControlDrag && DragDropMode == DragDropMode.AnyLevel;
            }
            else
            {
                // 单控件内拖拽：按原有逻辑处理
                switch(DragDropMode)
                {
                    case DragDropMode.AnyLevel:
                        // 任意层级模式：允许所有有效的拖拽操作
                        canDrop = true;
                        break;
                    case DragDropMode.SameLevel:
                        // 同层级模式：只允许相同层级的节点间拖拽
                        canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                        break;
                    case DragDropMode.SameParentAndLevel:
                        // 同父节点同层级模式：只允许相同父节点下的节点间拖拽
                        canDrop = draggedNode.Parent == targetNode.Parent;
                        break;
                }
            }

            if(canDrop)
            {
                if (isCrossControlDrag)
                {
                    // 跨控件拖拽：创建节点副本并添加到目标控件
                    TreeNode clonedNode = CloneNode(draggedNode);

                    // 插入到目标位置（目标节点之前）
                    TreeNodeCollection collection;
                    if(targetNode.Parent == null)
                        collection = this.Nodes; // 目标是根节点
                    else
                        collection = targetNode.Parent.Nodes; // 目标是子节点

                    int targetIndex = targetNode.Index;
                    collection.Insert(targetIndex, clonedNode);
                    this.SelectedNode = clonedNode; // 选中新添加的节点

                    // 注意：源节点的移除由源控件负责，这里只负责添加
                }
                else if(DragDropMode == DragDropMode.AnyLevel || DragDropMode == DragDropMode.SameLevel)
                {
                    // 单控件内跨层级或同层级拖拽：将节点插入到目标节点的父节点下，排在目标节点之前
                    TreeNodeCollection collection;
                    // 确定目标节点集合（根节点集合或父节点的子节点集合）
                    if(targetNode.Parent == null)
                        collection = this.Nodes;
                    else
                        collection = targetNode.Parent.Nodes;

                    // 从原位置移除被拖拽的节点
                    if(draggedNode.Parent == null)
                        this.Nodes.Remove(draggedNode);
                    else
                        draggedNode.Parent.Nodes.Remove(draggedNode);

                    // 插入到目标位置（目标节点之前）
                    int targetIndex = targetNode.Index;
                    collection.Insert(targetIndex, draggedNode);
                    this.SelectedNode = draggedNode; // 选中移动后的节点
                }
                else if(DragDropMode == DragDropMode.SameParentAndLevel)
                {
                    // 单控件内同父节点下排序：只在相同父节点的子节点间重新排序
                    if(draggedNode.Parent == null)
                    {
                        // 根节点间的排序
                        int targetIndex = targetNode.Index;
                        this.Nodes.Remove(draggedNode);
                        this.Nodes.Insert(targetIndex, draggedNode);
                        this.SelectedNode = draggedNode;
                    }
                    else
                    {
                        // 子节点间的排序
                        var parent = draggedNode.Parent;
                        int targetIndex = targetNode.Index;
                        parent.Nodes.Remove(draggedNode);
                        parent.Nodes.Insert(targetIndex, draggedNode);
                        this.SelectedNode = draggedNode;
                    }
                }
            }

            // 拖拽操作完成，重置所有拖拽状态
            ResetDragState();
        }

        /// <summary>
        /// 拖拽离开控件时隐藏ToolTip，但保持拖拽状态以支持跨控件拖拽。
        /// 该事件在拖拽对象离开控件边界时触发，用于清理拖拽相关的UI状态。
        /// 为了支持跨控件拖拽，不会重置isDragging状态，只清理UI相关的临时状态。
        /// 拖拽状态的最终重置将在DragDrop事件完成后或拖拽操作真正结束时进行。
        /// </summary>
        /// <param name="sender">事件源（FpiTreeView控件实例）</param>
        /// <param name="e">事件参数</param>
        private void FpiTreeView_DragLeave(object sender, EventArgs e)
        {
            // 隐藏拖拽提示（UI清理）
            toolTip1.Hide(this);

            // 注意：为了支持跨控件拖拽，这里不重置isDragging状态
            // isDragging状态将在拖拽操作真正完成时重置

            // 重置ToolTip位置记录（UI清理）
            lastToolTipPoint = System.Drawing.Point.Empty;

            // 注意：draggingNodeText保留，因为跨控件拖拽时目标控件可能需要这个信息
        }

        /// <summary>
        /// 获取节点的层级深度（根节点为0）。
        /// 该方法用于计算节点在树结构中的层级位置，支持拖拽模式中的同层级限制功能。
        /// 层级计算：根节点层级为0，每向下一级层级数加1。
        /// </summary>
        /// <param name="node">目标节点，不能为null</param>
        /// <returns>节点的层级数，根节点返回0，子节点返回1，孙节点返回2，以此类推</returns>
        private int GetNodeLevel(TreeNode node)
        {
            int level = 0;
            // 向上遍历父节点，计算层级深度
            while(node.Parent != null)
            {
                level++;
                node = node.Parent;
            }
            return level;
        }

        /// <summary>
        /// 检查指定节点是否属于指定的TreeView控件。
        /// 该方法用于判断节点是否为跨控件拖拽，通过递归遍历TreeView的所有节点来确定。
        /// 主要用于跨控件拖拽功能中区分本地节点和外部节点。
        /// </summary>
        /// <param name="targetNode">要检查的目标节点</param>
        /// <param name="treeView">要检查的TreeView控件</param>
        /// <returns>如果节点属于指定TreeView则返回true，否则返回false</returns>
        private bool IsNodeInTree(TreeNode targetNode, TreeView treeView)
        {
            // 递归检查所有根节点及其子节点
            foreach (TreeNode rootNode in treeView.Nodes)
            {
                if (IsNodeInSubTree(targetNode, rootNode))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 递归检查目标节点是否在指定子树中。
        /// 该辅助方法用于深度遍历树结构，查找目标节点。
        /// </summary>
        /// <param name="targetNode">要查找的目标节点</param>
        /// <param name="currentNode">当前检查的节点</param>
        /// <returns>如果找到目标节点则返回true，否则返回false</returns>
        private bool IsNodeInSubTree(TreeNode targetNode, TreeNode currentNode)
        {
            // 检查当前节点是否为目标节点
            if (currentNode == targetNode)
                return true;

            // 递归检查所有子节点
            foreach (TreeNode childNode in currentNode.Nodes)
            {
                if (IsNodeInSubTree(targetNode, childNode))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 深度克隆TreeNode节点及其所有子节点。
        /// 该方法用于跨控件拖拽时创建节点的完整副本，包括节点文本、标签、图标等所有属性。
        /// 递归克隆所有子节点，保持原有的树结构。
        /// </summary>
        /// <param name="originalNode">要克隆的原始节点</param>
        /// <returns>克隆后的新节点，包含完整的子树结构</returns>
        private TreeNode CloneNode(TreeNode originalNode)
        {
            // 创建新节点并复制基本属性
            TreeNode clonedNode = new TreeNode(originalNode.Text)
            {
                Tag = originalNode.Tag,
                ImageIndex = originalNode.ImageIndex,
                SelectedImageIndex = originalNode.SelectedImageIndex,
                StateImageIndex = originalNode.StateImageIndex,
                ToolTipText = originalNode.ToolTipText,
                Name = originalNode.Name,
                Checked = originalNode.Checked,
                ForeColor = originalNode.ForeColor,
                BackColor = originalNode.BackColor,
                NodeFont = originalNode.NodeFont
            };

            // 递归克隆所有子节点
            foreach (TreeNode childNode in originalNode.Nodes)
            {
                clonedNode.Nodes.Add(CloneNode(childNode));
            }

            return clonedNode;
        }

        /// <summary>
        /// 重置拖拽状态，清理所有拖拽相关的临时数据。
        /// 该方法在拖拽操作完成或取消时调用，确保控件状态正确重置。
        /// </summary>
        private void ResetDragState()
        {
            isDragging = false;
            draggingNodeText = null;
            lastToolTipPoint = System.Drawing.Point.Empty;
        }
    }
}