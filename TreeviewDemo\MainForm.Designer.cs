﻿namespace TreeviewDemo
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;
        private TreeviewDemo.FpiUiTreeView uiTreeView1;
        private TreeviewDemo.FpiTreeView fpiTreeView1;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.ComboBox comboBoxDragMode;
        private System.Windows.Forms.Panel panelTop;


        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.uiTreeView1 = new TreeviewDemo.FpiUiTreeView();
            this.fpiTreeView1 = new TreeviewDemo.FpiTreeView();
            this.comboBoxDragMode = new System.Windows.Forms.ComboBox();
            this.panelTop = new System.Windows.Forms.Panel();

            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.panelTop.SuspendLayout();
            this.SuspendLayout();
            // 
            // uiTreeView1
            // 
            this.uiTreeView1.Dock = System.Windows.Forms.DockStyle.Left;
            this.uiTreeView1.Location = new System.Drawing.Point(0, 75);
            this.uiTreeView1.Name = "uiTreeView1";
            this.uiTreeView1.SelectedNode = null;
            this.uiTreeView1.Size = new System.Drawing.Size(350, 375);
            this.uiTreeView1.TabIndex = 0;
            // 
            // fpiTreeView1
            // 
            this.fpiTreeView1.AllowDrop = true;
            this.fpiTreeView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.fpiTreeView1.DragDropMode = TreeviewDemo.DragDropMode.SameParentAndLevel;
            this.fpiTreeView1.Location = new System.Drawing.Point(350, 75);
            this.fpiTreeView1.Name = "fpiTreeView1";
            this.fpiTreeView1.Size = new System.Drawing.Size(450, 375);
            this.fpiTreeView1.TabIndex = 1;
            // 
            // comboBoxDragMode
            // 
            this.comboBoxDragMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxDragMode.FormattingEnabled = true;
            this.comboBoxDragMode.Items.AddRange(new object[] {
            "任意层级可拖拽",
            "同层级可拖拽",
            "同层级同父节点可拖拽"});
            this.comboBoxDragMode.Location = new System.Drawing.Point(16, 10);
            this.comboBoxDragMode.Name = "comboBoxDragMode";
            this.comboBoxDragMode.Size = new System.Drawing.Size(250, 24);
            this.comboBoxDragMode.TabIndex = 2;
            // 
            // panelTop
            //

            this.panelTop.Controls.Add(this.comboBoxDragMode);
            this.panelTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelTop.Location = new System.Drawing.Point(0, 35);
            this.panelTop.Name = "panelTop";
            this.panelTop.Size = new System.Drawing.Size(800, 40);
            this.panelTop.TabIndex = 3;

            // 
            // toolTip1
            // 
            this.toolTip1.IsBalloon = true;
            // 
            // MainForm
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.fpiTreeView1);
            this.Controls.Add(this.uiTreeView1);
            this.Controls.Add(this.panelTop);
            this.Name = "MainForm";
            this.ShowIcon = false;
            this.Text = "TreeView 拖拽排序示例";
            this.ZoomScaleRect = new System.Drawing.Rectangle(15, 15, 800, 450);
            this.panelTop.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
    }
}

