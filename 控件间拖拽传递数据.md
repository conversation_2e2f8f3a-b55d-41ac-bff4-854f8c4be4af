控件间拖拽传递数据

（For internal use only）

+:--------------:+:--------------------:+:----:+:-----:+:------------:+
| **拟制:**      | **刘晓鹏**           |      | *     | *            |
|                |                      |      | *日期 | *202-06-10** |
| **Prepared     |                      |      | ：**  |              |
| by**           |                      |      |       |              |
|                |                      |      | **D   |              |
|                |                      |      | ate** |              |
+----------------+----------------------+------+-------+--------------+
| **审核:**      |                      |      | *     |              |
|                |                      |      | *日期 |              |
| **Reviewed     |                      |      | ：**  |              |
| by**           |                      |      |       |              |
|                |                      |      | **D   |              |
|                |                      |      | ate** |              |
+----------------+----------------------+------+-------+--------------+
| **审核:**      |                      |      | *     |              |
|                |                      |      | *日期 |              |
| **Reviewed     |                      |      | ：**  |              |
| by**           |                      |      |       |              |
|                |                      |      | **D   |              |
|                |                      |      | ate** |              |
+----------------+----------------------+------+-------+--------------+
| **批准:**      |                      |      | *     |              |
|                |                      |      | *日期 |              |
| **Granted by** |                      |      | ：**  |              |
|                |                      |      |       |              |
|                |                      |      | **D   |              |
|                |                      |      | ate** |              |
+----------------+----------------------+------+-------+--------------+

![](./images/media/image1.wmf)

聚光科技（杭州）股份有限公司

FOCUSED PHOTONICS (HANGZHOU) INC.

版权所有 侵权必究

All rights reserved

# 

目录

[控件间拖拽传递数据 [1](#_Toc140572391)](#_Toc140572391)

[1 问题来源 [1](#问题来源)](#问题来源)

[2 问题描述 [1](#问题描述)](#问题描述)

[3 原因分析 [1](#原因分析)](#原因分析)

[4 解决方案 [2](#解决方案)](#解决方案)

[4.1 拖动流程 [2](#拖动流程)](#拖动流程)

[4.2 预先准备 [2](#预先准备)](#预先准备)

[4.3 ItemDrag事件 [2](#itemdrag事件)](#itemdrag事件)

[4.4 DragLeave事件 [3](#dragleave事件)](#dragleave事件)

[4.5 DragEnter事件 [3](#dragenter事件)](#dragenter事件)

[4.6 DragOver事件 [4](#dragover事件)](#dragover事件)

[4.7 DragDrop事件 [4](#dragdrop事件)](#dragdrop事件)

[5 实施效果评估 [5](#实施效果评估)](#实施效果评估)

[6 标准化 [5](#标准化)](#标准化)

[7 参考文献 [5](#参考文献)](#参考文献)

[]{#_Toc140572391 .anchor}控件间拖拽传递数据

【摘要】：SIA3000小型化仪表维护软件有一个流路编辑功能。左侧是一个Listview，盛放基本动作项，右侧也是一个Listview，盛放基本动作组合成的动作包。要求实现左侧基本动作列表拖动项目到右侧来添加动作包子项，并实现右侧列表内部拖动排序。因此总结控件间拖拽传递数据的方法。

【关键词】： 控件间拖拽，传递数据

# 问题来源

项目：SIA3000小型化仪表维护软件

# 问题描述

SIA3000小型化仪表维护软件有一个流路编辑功能。左侧是一个Listview，盛放基本动作项，右侧也是一个Listview，盛放基本动作组合成的动作包。要求实现左侧基本动作列表拖动项目到右侧来添加动作包子项，并实现右侧列表内部拖动排序。

# 原因分析

# 解决方案

## 拖动流程

> 将A中的数据拖放的B中，整体流程如下：
>
> 鼠标点击A中的数据（MouseDown、ItemDrag）-\>鼠标移动(MouseMove)-\>出源数据边界，即出A(DragLeave)-\>进入目标边界，进入B(DragEnter)-\>在B中移动，选择放数据的位置，即拖动效果(DragOver)-\>抬起鼠标(MouseUp)-\>将A数据放到B中，拖放结束
> (DragDrop)。

## 预先准备

> 为实现拖拽传递数据，首先要开启数据源与数据接收方两控件的"AllowDrop"属性，表明允许接受用户拖放文件的数据。

## ItemDrag事件

> 对数据源控件来说，鼠标按下开始拖动项时，触发的是控件的"ItemDrag"事件，事件处理程序写法如下：

1.  */// \<summary\>*

2.  */// 拖拽ListView中的Item时触发事件*

3.  */// \</summary\>*

4.  */// \<param name=\"sender\"\>ListView\</param\>*

5.  */// \<param name=\"e\"\>参数\</param\>*

6.  private void ListDragSource_ItemDrag(object sender, ItemDragEventArgs e)

7.  {

8.      ListView lstView = sender as ListView;

9.      if (lstView != null)

10.     {

11.         lstView.DoDragDrop(lstView.SelectedItems, DragDropEffects.Move);

12.     }

13. }

> 以上事件处理程序中，调用ListDragSource控件的"DoDragDrop"方法来开始拖放操作。方法有两个参数，第一个参数是odiect类型对象，代表的是拖拽事件传递的数据。此例中，我们把当前选中项目传递给作为此参数；第二个参数是"DragDropEffects"类型枚举，代表的是拖拽效果，它将影响拖拽过程中鼠标指针的动画效果，也可通过此参数在数据接收控件的"DragEnter"方法中对拖拽操作做过滤。
>
> 对于没有"ItemDrag"事件的控件，可以在其"MouseDown"、"MouseMove"事件中处此业务逻辑。

## DragLeave事件

> 在将对象拖出数据源控件的元素边界，且未放置时，触发的是控件的"DragLeave"事件，事件处理程序写法如下：

1.  */// \<summary\>*

2.  */// 当ListView中的项(Item)托离ListView时触发*

3.  */// \</summary\>*

4.  */// \<param name=\"sender\"\>\</param\>*

5.  */// \<param name=\"e\"\>\</param\>*

6.  private void ListDragTarget_DragLeave(object sender, EventArgs e) 

7.  {

8.      ListView lstView = sender as ListView;

9.      foreach (ListViewItem item in lstView.SelectedItems)

10.     {

11.         lstView.Items.Remove(item);

12.     }

13. }

> 如果需要在数据拖离控件时做数据删除等操作，可在此事件处理方法中进行处理。

## DragEnter事件

> 在将数据对象拖放到数据接收控件的边界内时，首先触发的是此控件的"DragEnter"事件，事件处理程序写法如下：

1.  */// \<summary\>*

2.  */// 当拖入ListView时触发*

3.  */// \</summary\>*

4.  */// \<param name=\"sender\"\>\</param\>*

5.  */// \<param name=\"e\"\>\</param\>*

6.  private void ListDragTarget_DragEnter(object sender, System.Windows.Forms.DragEventArgs e)

7.  {

8.      *// 从Listview控件传递数据*

9.      for (int i = 0; i \<= e.Data.GetFormats().Length - 1; i++)

10.     {

11.         if (e.Data.GetFormats()\[i\].Equals(\"System.Windows.Forms.ListView+SelectedListViewItemCollection\"))

12.         {

13.          e.Effect = DragDropEffects.Move;

14.         }

15.     }

16. 

17.  *// 判断传入数据是否为指定类型*

18.  if (e.Data.GetDataPresent(DataFormats.FileDrop))

19.  {

20.      e.Effect = DragDropEffects.Move;

21.  }

22. }

> 在此事件中，我们可以判断拖动进来的数据的类型，以此决定要不要响应此次拖动事件。若相应，则把"e.Effect"设置为与数据发送方"ItemDrag"事件处理方法中相同的值，否则不处理，或者设置值为"DragDropEffects.Null"。

## DragOver事件

> 数据对象在数据接收控件内部拖动，且未放置时，触发的是控件的"DragOver"事件，事件处理程序写法如下：

1.  */// \<summary\>*

2.  */// 当在ListView内拖动时触发*

3.  */// \</summary\>*

4.  */// \<param name=\"sender\"\>\</param\>*

5.  */// \<param name=\"e\"\>\</param\>*

6.  private void ListDragTarget_DragEnter(object sender, System.Windows.Forms.DragEventArgs e)

7.  {

8.  

9.  }

> 在此方法中，可以通过获取鼠标当前坐标，来做目标控件内的动画效果、是否可释放拖放判定（比如只有在数据接收控件内部的特定位置释放数据）等操作。

## DragDrop事件

> 在将数据对象拖放到数据接收控件的内并释放时，触发的是此控件的"DragDrop"事件，事件处理程序写法如下：

1.  */// \<summary\>*

2.  */// 拖拽完成时触发*

3.  */// \</summary\>*

4.  */// \<param name=\"sender\"\>\</param\>*

5.  */// \<param name=\"e\"\>\</param\>*

6.  private void ListDragTarget_DragDrop(object sender, DragEventArgs e)

7.  {

8.      ListView lstView = sender as ListView;

9.      System.Windows.Forms.ListView.SelectedListViewItemCollection items = (System.Windows.Forms.ListView.SelectedListViewItemCollection)e.Data.GetData(typeof(System.Windows.Forms.ListView.SelectedListViewItemCollection));

10.     if (items != null && items.Count \> 0)

11.     {

12.         foreach (ListViewItem item in items)

13.         {

14.             ListViewItem itemTemp = (ListViewItem)item.Clone();

15.             ItemTemp.Name = item.Text;

16.             if (!lstView.Items.ContainsKey(ItemTemp.Name))

17.             {

18.                 lstView.Items.Add(itemTemp);

19.             }

20.         }

21.     }

22. }

> 此事件处理程序中，可进行最终业务处理，进行数据解析、控件内数据位置移动等操作。

# 实施效果评估

> 使用上述介绍的方法，可以实现控件间拖拽传递数据，包括但不限定于Listview控件。

# 标准化

# 参考文献

  -----------------------------------------------------------------------
  序号   文献来源或出处    文献名称
  ------ ----------------- ----------------------------------------------
  1                        

  <USER>                        

  <GROUP>                        
  -----------------------------------------------------------------------
