using System;
using System.Windows.Forms;
using Sunny.UI;

namespace TreeviewDemo
{
    /// <summary>
    /// 拖拽模式枚举（与FpiTreeView一致）
    /// </summary>
    public enum FpiDragDropMode
    {
        /// <summary>任意层级下可互相拖拽</summary>
        AnyLevel,
        /// <summary>同一层级下可互相拖拽</summary>
        SameLevel,
        /// <summary>同一层级且同一父节点下可互相拖拽</summary>
        SameParentAndLevel
    }

    /// <summary>
    /// FpiUiTreeView：基于Sunny.UI.UITreeView的N级节点拖拽排序自定义控件。
    /// 支持节点链路提示、同级拖拽排序、外部属性访问等功能。
    /// </summary>
    public partial class FpiUiTreeView : UserControl
    {
        /// <summary>
        /// 拖拽模式，支持三种：任意层级、同层级、同层级同父节点
        /// </summary>
        public FpiDragDropMode DragDropMode { get; set; } = FpiDragDropMode.SameParentAndLevel;
        /// <summary>
        /// 内部UITreeView控件，承载实际的树结构显示与操作。
        /// </summary>
        private UITreeView innerTreeView;
        /// <summary>
        /// 拖拽提示用ToolTip控件。
        /// </summary>
        private ToolTip toolTip1 = new ToolTip();
        /// <summary>
        /// 当前正在拖拽的节点链路文本。
        /// </summary>
        private string draggingNodeText = null;
        /// <summary>
        /// 拖拽状态标记。
        /// </summary>
        private bool isDragging = false;
        /// <summary>
        /// 上一次ToolTip显示的鼠标点。
        /// </summary>
        private System.Drawing.Point lastToolTipPoint = System.Drawing.Point.Empty;

        /// <summary>
        /// 构造函数，初始化控件、事件绑定及布局。
        /// 该构造函数负责：
        /// 1. 创建内部UITreeView控件实例
        /// 2. 配置拖拽相关属性和布局
        /// 3. 绑定所有拖拽事件处理程序
        /// 4. 初始化ToolTip提示控件
        /// </summary>
        public FpiUiTreeView()
        {
            InitializeComponent();

            // 创建并配置内部UITreeView控件
            innerTreeView = new UITreeView();
            innerTreeView.AllowDrop = true; // 启用拖拽功能
            innerTreeView.Dock = DockStyle.Fill; // 填充整个用户控件
            this.Controls.Add(innerTreeView);

            // 配置ToolTip样式
            toolTip1.IsBalloon = true;

            // 绑定拖拽相关事件处理程序
            innerTreeView.ItemDrag += InnerTreeView_ItemDrag;     // 拖拽开始
            innerTreeView.DragEnter += InnerTreeView_DragEnter;   // 拖拽进入
            innerTreeView.DragDrop += InnerTreeView_DragDrop;     // 拖拽释放
            innerTreeView.DragOver += InnerTreeView_DragOver;     // 拖拽悬停
            innerTreeView.DragLeave += InnerTreeView_DragLeave;   // 拖拽离开
        }

        /// <summary>
        /// 获取内部UITreeView控件，便于外部访问和设置属性。
        /// 通过此属性可以直接访问Sunny.UI的UITreeView控件，
        /// 用于设置样式、主题、字体等UI相关属性。
        /// </summary>
        public UITreeView InnerTreeView => innerTreeView;

        /// <summary>
        /// 获取树节点集合。
        /// 提供对树结构根节点集合的直接访问，
        /// 用于添加、删除、遍历根级节点。
        /// </summary>
        public TreeNodeCollection Nodes => innerTreeView.Nodes;

        /// <summary>
        /// 获取或设置当前选中节点。
        /// 用于程序化控制节点选择状态，
        /// 在拖拽操作完成后会自动选中移动后的节点。
        /// </summary>
        public TreeNode SelectedNode
        {
            get => innerTreeView.SelectedNode;
            set => innerTreeView.SelectedNode = value;
        }

        /// <summary>
        /// 展开所有节点。
        /// 递归展开树中的所有节点，显示完整的树结构。
        /// 常用于初始化显示或调试时查看完整数据。
        /// </summary>
        public void ExpandAll() => innerTreeView.ExpandAll();

        /// <summary>
        /// 收起所有节点。
        /// 收起除根节点外的所有节点，只显示第一级节点。
        /// 用于简化视图或重置展开状态。
        /// </summary>
        public void CollapseAll() => innerTreeView.CollapseAll();

        /// <summary>
        /// 清空所有节点。
        /// 移除树中的所有节点，重置为空树状态。
        /// 常用于重新加载数据前的清理操作。
        /// </summary>
        public void Clear() => innerTreeView.Nodes.Clear();

        /// <summary>
        /// 获取指定节点的完整父链路（如"一级/二级/三级"）。
        /// 该方法用于构建节点的完整路径字符串，便于在拖拽提示中显示节点的层级关系。
        /// 路径格式：从根节点到当前节点的完整路径，使用"/"分隔各级节点名称。
        /// </summary>
        /// <param name="node">目标节点，如果为null则返回空字符串</param>
        /// <returns>节点的完整路径字符串，如"根节点/子节点/孙节点"</returns>
        private string GetNodePath(TreeNode node)
        {
            if(node == null) return string.Empty;

            // 从当前节点开始构建路径
            string path = node.Text;
            TreeNode parent = node.Parent;

            // 向上遍历所有父节点，构建完整路径
            while(parent != null)
            {
                path = parent.Text + "/" + path;
                parent = parent.Parent;
            }
            return path;
        }

        /// <summary>
        /// 获取节点的层级深度（根节点为0）。
        /// 该方法用于计算节点在树结构中的层级位置，支持拖拽模式中的同层级限制功能。
        /// 层级计算：根节点层级为0，每向下一级层级数加1。
        /// </summary>
        /// <param name="node">目标节点，不能为null</param>
        /// <returns>节点的层级数，根节点返回0，子节点返回1，孙节点返回2，以此类推</returns>
        private int GetNodeLevel(TreeNode node)
        {
            int level = 0;
            // 向上遍历父节点，计算层级深度
            while(node.Parent != null)
            {
                level++;
                node = node.Parent;
            }
            return level;
        }

        /// <summary>
        /// 拖拽开始事件，记录拖拽节点链路并启动拖拽操作。
        /// 该事件在用户开始拖拽树节点时触发，负责：
        /// 1. 记录被拖拽节点的完整路径信息
        /// 2. 设置拖拽状态标记
        /// 3. 启动系统拖拽操作
        /// 4. 拖拽结束后清理状态和UI提示
        /// </summary>
        /// <param name="sender">事件源（内部UITreeView控件实例）</param>
        /// <param name="e">拖拽事件参数，包含被拖拽的项目信息</param>
        private void InnerTreeView_ItemDrag(object sender, ItemDragEventArgs e)
        {
            // 检查拖拽项是否为TreeNode，并记录其路径信息
            if(e.Item is TreeNode node)
            {
                draggingNodeText = GetNodePath(node); // 记录拖拽节点的完整路径
                isDragging = true; // 设置拖拽状态标记
            }

            // 启动系统拖拽操作，设置拖拽效果为移动
            innerTreeView.DoDragDrop(e.Item, DragDropEffects.Move);

            // 拖拽操作结束后清理状态
            draggingNodeText = null;
            isDragging = false;
            toolTip1.Hide(innerTreeView); // 隐藏可能残留的提示
        }

        /// <summary>
        /// 拖拽进入控件时设置拖拽效果。
        /// 该事件在拖拽对象首次进入控件边界时触发，用于设置拖拽操作的视觉效果。
        /// 这里统一设置为移动效果，具体的拖拽限制在DragOver事件中处理。
        /// </summary>
        /// <param name="sender">事件源（内部UITreeView控件实例）</param>
        /// <param name="e">拖拽事件参数</param>
        private void InnerTreeView_DragEnter(object sender, DragEventArgs e)
        {
            // 设置拖拽效果为移动，允许拖拽操作继续
            e.Effect = DragDropEffects.Move;
        }

        /// <summary>
        /// 拖拽过程中显示链路提示，仅在允许拖拽区域显示。
        /// 该方法在拖拽对象在控件上方移动时持续触发，用于：
        /// 1. 实时判断当前位置是否允许放置拖拽对象
        /// 2. 根据拖拽模式限制拖拽范围
        /// 3. 显示拖拽节点的完整路径提示
        /// 4. 设置合适的拖拽效果（允许/禁止）
        /// </summary>
        /// <param name="sender">事件源（内部UITreeView控件实例）</param>
        /// <param name="e">拖拽事件参数，包含鼠标位置和拖拽数据</param>
        private void InnerTreeView_DragOver(object sender, DragEventArgs e)
        {
            // 检查拖拽状态和节点文本是否有效，无效则禁止拖拽并隐藏提示
            if(!isDragging || string.IsNullOrEmpty(draggingNodeText))
            {
                toolTip1.Hide(innerTreeView);
                e.Effect = DragDropEffects.None;
                return;
            }

            // 将屏幕坐标转换为控件内部坐标
            var pt = innerTreeView.PointToClient(new System.Drawing.Point(e.X, e.Y));
            // 获取鼠标位置下的目标节点
            var targetNode = innerTreeView.GetNodeAt(pt);
            // 从拖拽数据中获取被拖拽的节点
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            bool canDrop = false;

            // 拖拽模式判断：确保拖拽节点和目标节点都存在且不是同一个节点
            if(draggedNode != null && targetNode != null && draggedNode != targetNode)
            {
                switch(DragDropMode)
                {
                    case FpiDragDropMode.AnyLevel:
                        // 任意层级模式：允许所有有效的拖拽操作
                        canDrop = true;
                        break;
                    case FpiDragDropMode.SameLevel:
                        // 同层级模式：只允许相同层级的节点间拖拽
                        canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                        break;
                    case FpiDragDropMode.SameParentAndLevel:
                        // 同父节点同层级模式：只允许相同父节点下的节点间拖拽
                        canDrop = draggedNode.Parent == targetNode.Parent;
                        break;
                }
            }

            if(canDrop)
            {
                // 允许拖拽：设置移动效果并显示路径提示
                e.Effect = DragDropEffects.Move;
                // 只有当鼠标位置发生变化时才更新ToolTip，避免频繁刷新
                if(lastToolTipPoint != pt)
                {
                    toolTip1.Show($"正在拖拽：{draggingNodeText}", innerTreeView, pt.X + 20, pt.Y + 20, int.MaxValue);
                    lastToolTipPoint = pt;
                }
            }
            else
            {
                // 禁止拖拽：设置无效果并隐藏提示
                e.Effect = DragDropEffects.None;
                toolTip1.Hide(innerTreeView);
                lastToolTipPoint = System.Drawing.Point.Empty;
            }
        }

        /// <summary>
        /// 拖拽释放时处理节点排序和父节点限制，支持N级节点和多种拖拽模式。
        /// 该事件在用户释放鼠标完成拖拽操作时触发，负责：
        /// 1. 清理拖拽状态和UI提示
        /// 2. 验证拖拽操作的有效性
        /// 3. 根据拖拽模式执行相应的节点移动操作
        /// 4. 更新选中状态到移动后的节点
        /// </summary>
        /// <param name="sender">事件源（内部UITreeView控件实例）</param>
        /// <param name="e">拖拽事件参数，包含拖拽数据和鼠标位置</param>
        private void InnerTreeView_DragDrop(object sender, DragEventArgs e)
        {
            // 清理拖拽状态和UI提示
            toolTip1.Hide(innerTreeView);
            isDragging = false;
            draggingNodeText = null;
            lastToolTipPoint = System.Drawing.Point.Empty;

            // 获取被拖拽的节点和目标位置信息
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            if(draggedNode == null) return; // 无效的拖拽数据

            // 将屏幕坐标转换为控件坐标，获取目标节点
            var pt = innerTreeView.PointToClient(new System.Drawing.Point(e.X, e.Y));
            var targetNode = innerTreeView.GetNodeAt(pt);
            if(targetNode == null || draggedNode == targetNode) return; // 无效的目标位置

            // 根据拖拽模式验证操作有效性
            bool canDrop = false;
            switch(DragDropMode)
            {
                case FpiDragDropMode.AnyLevel:
                    // 任意层级模式：允许所有有效的拖拽操作
                    canDrop = true;
                    break;
                case FpiDragDropMode.SameLevel:
                    // 同层级模式：只允许相同层级的节点间拖拽
                    canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                    break;
                case FpiDragDropMode.SameParentAndLevel:
                    // 同父节点同层级模式：只允许相同父节点下的节点间拖拽
                    canDrop = draggedNode.Parent == targetNode.Parent;
                    break;
            }

            // 执行节点移动操作
            if(canDrop)
            {
                if(DragDropMode == FpiDragDropMode.AnyLevel || DragDropMode == FpiDragDropMode.SameLevel)
                {
                    // 跨层级或同层级模式：插入到目标节点父节点下，排在目标节点之前
                    TreeNodeCollection collection;
                    if(targetNode.Parent == null)
                        collection = innerTreeView.Nodes; // 目标是根节点
                    else
                        collection = targetNode.Parent.Nodes; // 目标是子节点

                    // 从原位置移除拖拽节点
                    if(draggedNode.Parent == null)
                        innerTreeView.Nodes.Remove(draggedNode);
                    else
                        draggedNode.Parent.Nodes.Remove(draggedNode);

                    // 插入到目标位置
                    int targetIndex = targetNode.Index;
                    collection.Insert(targetIndex, draggedNode);
                    innerTreeView.SelectedNode = draggedNode; // 选中移动后的节点
                }
                else if(DragDropMode == FpiDragDropMode.SameParentAndLevel)
                {
                    // 同父节点同层级模式：只允许同一父节点下的排序
                    if(draggedNode.Parent == null)
                    {
                        // 根节点间的排序
                        int targetIndex = targetNode.Index;
                        innerTreeView.Nodes.Remove(draggedNode);
                        innerTreeView.Nodes.Insert(targetIndex, draggedNode);
                        innerTreeView.SelectedNode = draggedNode;
                    }
                    else
                    {
                        // 子节点间的排序
                        var parent = draggedNode.Parent;
                        int targetIndex = targetNode.Index;
                        parent.Nodes.Remove(draggedNode);
                        parent.Nodes.Insert(targetIndex, draggedNode);
                        innerTreeView.SelectedNode = draggedNode;
                    }
                }
            }
        }

        /// <summary>
        /// 拖拽离开控件时隐藏ToolTip并重置状态。
        /// 该事件在拖拽对象离开控件边界时触发，用于清理拖拽过程中的临时状态：
        /// 1. 隐藏路径提示ToolTip
        /// 2. 重置拖拽状态标记
        /// 3. 清空拖拽节点文本缓存
        /// 4. 重置ToolTip位置记录
        /// </summary>
        /// <param name="sender">事件源（内部UITreeView控件实例）</param>
        /// <param name="e">事件参数</param>
        private void InnerTreeView_DragLeave(object sender, EventArgs e)
        {
            // 清理拖拽过程中的所有临时状态
            toolTip1.Hide(innerTreeView);                        // 隐藏路径提示
            isDragging = false;                                  // 重置拖拽状态
            draggingNodeText = null;                            // 清空节点文本缓存
            lastToolTipPoint = System.Drawing.Point.Empty;      // 重置ToolTip位置
        }

        /// <summary>
        /// Designer 兼容初始化。
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.Name = "FpiUiTreeView";
            this.Size = new System.Drawing.Size(300, 400);
            this.ResumeLayout(false);
        }
    }
}