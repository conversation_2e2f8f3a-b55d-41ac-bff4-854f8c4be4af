using System;
using System.Windows.Forms;
using Sunny.UI;

namespace TreeviewDemo
{
    /// <summary>
    /// 拖拽模式枚举（与FpiTreeView一致）
    /// </summary>
    public enum FpiDragDropMode
    {
        /// <summary>任意层级下可互相拖拽</summary>
        AnyLevel,
        /// <summary>同一层级下可互相拖拽</summary>
        SameLevel,
        /// <summary>同一层级且同一父节点下可互相拖拽</summary>
        SameParentAndLevel
    }

    /// <summary>
    /// FpiUiTreeView：基于Sunny.UI.UITreeView的N级节点拖拽排序自定义控件。
    /// 支持节点链路提示、同级拖拽排序、外部属性访问等功能。
    /// </summary>
    public partial class FpiUiTreeView : UserControl
    {
        /// <summary>
        /// 拖拽模式，支持三种：任意层级、同层级、同层级同父节点
        /// </summary>
        public FpiDragDropMode DragDropMode { get; set; } = FpiDragDropMode.SameParentAndLevel;
        /// <summary>
        /// 内部UITreeView控件，承载实际的树结构显示与操作。
        /// </summary>
        private UITreeView innerTreeView;
        /// <summary>
        /// 拖拽提示用ToolTip控件。
        /// </summary>
        private ToolTip toolTip1 = new ToolTip();
        /// <summary>
        /// 当前正在拖拽的节点链路文本。
        /// </summary>
        private string draggingNodeText = null;
        /// <summary>
        /// 拖拽状态标记。
        /// </summary>
        private bool isDragging = false;
        /// <summary>
        /// 上一次ToolTip显示的鼠标点。
        /// </summary>
        private System.Drawing.Point lastToolTipPoint = System.Drawing.Point.Empty;

        /// <summary>
        /// 构造函数，初始化控件、事件绑定及布局。
        /// 该构造函数负责：
        /// 1. 创建内部UITreeView控件实例
        /// 2. 配置拖拽相关属性和布局
        /// 3. 绑定所有拖拽事件处理程序
        /// 4. 初始化ToolTip提示控件
        /// </summary>
        public FpiUiTreeView()
        {
            InitializeComponent();

            // 创建并配置内部UITreeView控件
            innerTreeView = new UITreeView();
            innerTreeView.AllowDrop = true; // 启用拖拽功能
            innerTreeView.Dock = DockStyle.Fill; // 填充整个用户控件
            this.Controls.Add(innerTreeView);

            // 配置ToolTip样式
            toolTip1.IsBalloon = true;

            // 绑定拖拽相关事件处理程序
            innerTreeView.ItemDrag += InnerTreeView_ItemDrag;     // 拖拽开始
            innerTreeView.DragEnter += InnerTreeView_DragEnter;   // 拖拽进入
            innerTreeView.DragDrop += InnerTreeView_DragDrop;     // 拖拽释放
            innerTreeView.DragOver += InnerTreeView_DragOver;     // 拖拽悬停
            innerTreeView.DragLeave += InnerTreeView_DragLeave;   // 拖拽离开
        }

        /// <summary>
        /// 获取内部UITreeView控件，便于外部访问和设置属性。
        /// 通过此属性可以直接访问Sunny.UI的UITreeView控件，
        /// 用于设置样式、主题、字体等UI相关属性。
        /// </summary>
        public UITreeView InnerTreeView => innerTreeView;

        /// <summary>
        /// 获取树节点集合。
        /// 提供对树结构根节点集合的直接访问，
        /// 用于添加、删除、遍历根级节点。
        /// </summary>
        public TreeNodeCollection Nodes => innerTreeView.Nodes;

        /// <summary>
        /// 获取或设置当前选中节点。
        /// 用于程序化控制节点选择状态，
        /// 在拖拽操作完成后会自动选中移动后的节点。
        /// </summary>
        public TreeNode SelectedNode
        {
            get => innerTreeView.SelectedNode;
            set => innerTreeView.SelectedNode = value;
        }

        /// <summary>
        /// 展开所有节点。
        /// 递归展开树中的所有节点，显示完整的树结构。
        /// 常用于初始化显示或调试时查看完整数据。
        /// </summary>
        public void ExpandAll() => innerTreeView.ExpandAll();

        /// <summary>
        /// 收起所有节点。
        /// 收起除根节点外的所有节点，只显示第一级节点。
        /// 用于简化视图或重置展开状态。
        /// </summary>
        public void CollapseAll() => innerTreeView.CollapseAll();

        /// <summary>
        /// 清空所有节点。
        /// 移除树中的所有节点，重置为空树状态。
        /// 常用于重新加载数据前的清理操作。
        /// </summary>
        public void Clear() => innerTreeView.Nodes.Clear();

        /// <summary>
        /// 获取指定节点的完整父链路（如"一级/二级/三级"）。
        /// 该方法用于构建节点的完整路径字符串，便于在拖拽提示中显示节点的层级关系。
        /// 路径格式：从根节点到当前节点的完整路径，使用"/"分隔各级节点名称。
        /// </summary>
        /// <param name="node">目标节点，如果为null则返回空字符串</param>
        /// <returns>节点的完整路径字符串，如"根节点/子节点/孙节点"</returns>
        private string GetNodePath(TreeNode node)
        {
            if(node == null) return string.Empty;

            // 从当前节点开始构建路径
            string path = node.Text;
            TreeNode parent = node.Parent;

            // 向上遍历所有父节点，构建完整路径
            while(parent != null)
            {
                path = parent.Text + "/" + path;
                parent = parent.Parent;
            }
            return path;
        }

        /// <summary>
        /// 获取节点的层级深度（根节点为0）。
        /// 该方法用于计算节点在树结构中的层级位置，支持拖拽模式中的同层级限制功能。
        /// 层级计算：根节点层级为0，每向下一级层级数加1。
        /// </summary>
        /// <param name="node">目标节点，不能为null</param>
        /// <returns>节点的层级数，根节点返回0，子节点返回1，孙节点返回2，以此类推</returns>
        private int GetNodeLevel(TreeNode node)
        {
            int level = 0;
            // 向上遍历父节点，计算层级深度
            while(node.Parent != null)
            {
                level++;
                node = node.Parent;
            }
            return level;
        }

        /// <summary>
        /// 拖拽开始事件，记录拖拽节点链路并启动拖拽。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void InnerTreeView_ItemDrag(object sender, ItemDragEventArgs e)
        {
            if(e.Item is TreeNode node)
            {
                draggingNodeText = GetNodePath(node);
                isDragging = true;
            }
            innerTreeView.DoDragDrop(e.Item, DragDropEffects.Move);
            draggingNodeText = null;
            isDragging = false;
            toolTip1.Hide(innerTreeView);
        }

        /// <summary>
        /// 拖拽进入控件时设置拖拽效果。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void InnerTreeView_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.Move;
        }

        /// <summary>
        /// 拖拽过程中显示链路提示，仅在允许拖拽区域显示。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void InnerTreeView_DragOver(object sender, DragEventArgs e)
        {
            if(!isDragging || string.IsNullOrEmpty(draggingNodeText))
            {
                toolTip1.Hide(innerTreeView);
                e.Effect = DragDropEffects.None;
                return;
            }
            var pt = innerTreeView.PointToClient(new System.Drawing.Point(e.X, e.Y));
            var targetNode = innerTreeView.GetNodeAt(pt);
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            bool canDrop = false;
            // 拖拽模式判断
            if(draggedNode != null && targetNode != null && draggedNode != targetNode)
            {
                switch(DragDropMode)
                {
                    case FpiDragDropMode.AnyLevel:
                        canDrop = true;
                        break;
                    case FpiDragDropMode.SameLevel:
                        canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                        break;
                    case FpiDragDropMode.SameParentAndLevel:
                        canDrop = draggedNode.Parent == targetNode.Parent;
                        break;
                }
            }
            if(canDrop)
            {
                e.Effect = DragDropEffects.Move;
                if(lastToolTipPoint != pt)
                {
                    toolTip1.Show($"正在拖拽：{draggingNodeText}", innerTreeView, pt.X + 20, pt.Y + 20, int.MaxValue);
                    lastToolTipPoint = pt;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
                toolTip1.Hide(innerTreeView);
                lastToolTipPoint = System.Drawing.Point.Empty;
            }
        }

        /// <summary>
        /// 拖拽释放时处理节点排序和父节点限制，支持N级节点和多种拖拽模式。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void InnerTreeView_DragDrop(object sender, DragEventArgs e)
        {
            toolTip1.Hide(innerTreeView);
            isDragging = false;
            draggingNodeText = null;
            lastToolTipPoint = System.Drawing.Point.Empty;
            var draggedNode = e.Data.GetData(typeof(TreeNode)) as TreeNode;
            if(draggedNode == null) return;
            var pt = innerTreeView.PointToClient(new System.Drawing.Point(e.X, e.Y));
            var targetNode = innerTreeView.GetNodeAt(pt);
            if(targetNode == null || draggedNode == targetNode) return;
            // 拖拽模式判断
            bool canDrop = false;
            switch(DragDropMode)
            {
                case FpiDragDropMode.AnyLevel:
                    canDrop = true;
                    break;
                case FpiDragDropMode.SameLevel:
                    canDrop = GetNodeLevel(draggedNode) == GetNodeLevel(targetNode);
                    break;
                case FpiDragDropMode.SameParentAndLevel:
                    canDrop = draggedNode.Parent == targetNode.Parent;
                    break;
            }
            if(canDrop)
            {
                if(DragDropMode == FpiDragDropMode.AnyLevel || DragDropMode == FpiDragDropMode.SameLevel)
                {
                    // 插入到目标节点父节点下，排在目标节点之前
                    TreeNodeCollection collection;
                    if(targetNode.Parent == null)
                        collection = innerTreeView.Nodes;
                    else
                        collection = targetNode.Parent.Nodes;
                    if(draggedNode.Parent == null)
                        innerTreeView.Nodes.Remove(draggedNode);
                    else
                        draggedNode.Parent.Nodes.Remove(draggedNode);
                    int targetIndex = targetNode.Index;
                    collection.Insert(targetIndex, draggedNode);
                    innerTreeView.SelectedNode = draggedNode;
                }
                else if(DragDropMode == FpiDragDropMode.SameParentAndLevel)
                {
                    // 只允许同一父节点下排序
                    if(draggedNode.Parent == null)
                    {
                        int targetIndex = targetNode.Index;
                        innerTreeView.Nodes.Remove(draggedNode);
                        innerTreeView.Nodes.Insert(targetIndex, draggedNode);
                        innerTreeView.SelectedNode = draggedNode;
                    }
                    else
                    {
                        var parent = draggedNode.Parent;
                        int targetIndex = targetNode.Index;
                        parent.Nodes.Remove(draggedNode);
                        parent.Nodes.Insert(targetIndex, draggedNode);
                        innerTreeView.SelectedNode = draggedNode;
                    }
                }
            }
        }

        /// <summary>
        /// 拖拽离开控件时隐藏ToolTip并重置状态。
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void InnerTreeView_DragLeave(object sender, EventArgs e)
        {
            toolTip1.Hide(innerTreeView);
            isDragging = false;
            draggingNodeText = null;
            lastToolTipPoint = System.Drawing.Point.Empty;
        }

        /// <summary>
        /// Designer 兼容初始化。
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.Name = "FpiUiTreeView";
            this.Size = new System.Drawing.Size(300, 400);
            this.ResumeLayout(false);
        }
    }
}