﻿using Sunny.UI;
using System;
using System.Windows.Forms;

namespace TreeviewDemo
{
    /// <summary>
    /// 主窗体，演示FpiUiTreeView（Sunny.UI版）与FpiUiTreeViewStandard（标准TreeView版）拖拽排序功能对比
    /// </summary>
    public partial class MainForm : UIForm
    {
        /// <summary>
        /// 构造函数，初始化窗体
        /// </summary>
        public MainForm()
        {
            InitializeComponent();
            InitializeContextMenus(); // 初始化右键菜单
        }

        /// <summary>
        /// 初始化右键菜单，为TreeView控件添加常用操作菜单
        /// </summary>
        private void InitializeContextMenus()
        {
            // 创建右键菜单
            var contextMenu = new ContextMenuStrip();

            // 添加菜单项
            var addNodeItem = new ToolStripMenuItem("➕ 添加子节点");
            var deleteNodeItem = new ToolStripMenuItem("❌ 删除节点");
            var renameNodeItem = new ToolStripMenuItem("✏️ 重命名节点");
            var expandAllItem = new ToolStripMenuItem("📂 展开所有");
            var collapseAllItem = new ToolStripMenuItem("📁 收起所有");
            var refreshDataItem = new ToolStripMenuItem("🔄 刷新数据");

            // 绑定事件处理程序
            addNodeItem.Click += AddNodeItem_Click;
            deleteNodeItem.Click += DeleteNodeItem_Click;
            renameNodeItem.Click += RenameNodeItem_Click;
            expandAllItem.Click += ExpandAllItem_Click;
            collapseAllItem.Click += CollapseAllItem_Click;
            refreshDataItem.Click += RefreshDataItem_Click;

            // 添加到菜单
            contextMenu.Items.AddRange(new ToolStripItem[]
            {
                addNodeItem,
                deleteNodeItem,
                new ToolStripSeparator(),
                renameNodeItem,
                new ToolStripSeparator(),
                expandAllItem,
                collapseAllItem,
                new ToolStripSeparator(),
                refreshDataItem
            });

            // 为两个TreeView控件设置右键菜单
            fpiTreeView1.ContextMenuStrip = contextMenu;
            uiTreeView1.InnerTreeView.ContextMenuStrip = contextMenu;
        }

        /// <summary>
        /// 窗体加载时，分别为左右两个TreeView控件填充多层级虚拟数据，并初始化拖拽模式下拉框。
        /// 该方法在窗体首次显示时执行，负责：
        /// 1. 为两个TreeView控件填充相同的测试数据
        /// 2. 展开所有节点以便观察层级结构
        /// 3. 设置拖拽模式下拉框的默认选项
        /// 4. 绑定拖拽模式切换事件处理程序
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 填充Sunny.UI自定义TreeView控件（左侧）
            InitTreeView(uiTreeView1.Nodes);
            uiTreeView1.ExpandAll(); // 展开所有节点以显示完整结构

            // 填充标准TreeView自定义控件（右侧）
            InitTreeView(fpiTreeView1.Nodes);
            fpiTreeView1.ExpandAll(); // 展开所有节点以显示完整结构

            // 初始化拖拽模式下拉框
            comboBoxDragMode.SelectedIndex = 2; // 默认选择"同层级同父节点可拖拽"模式
            comboBoxDragMode.SelectedIndexChanged += ComboBoxDragMode_SelectedIndexChanged;
        }

        /// <summary>
        /// 填充多层级虚拟数据到指定节点集合。
        /// 该方法创建一个丰富的多层级树形结构，用于演示拖拽排序功能：
        /// - 第1层：企业组织结构根节点
        /// - 第2层：部门节点（技术部、销售部、人事部等）
        /// - 第3层：团队/项目节点
        /// - 第4层：员工节点
        /// - 第5层：任务节点
        /// 包含不同类型的节点以测试各种拖拽场景和模式限制。
        /// </summary>
        /// <param name="nodes">目标节点集合，通常是TreeView的Nodes属性</param>
        private void InitTreeView(TreeNodeCollection nodes)
        {
            nodes.Clear(); // 清空现有节点

            // === 第一层：企业组织结构根节点 ===
            var companyNode = nodes.Add("🏢 聚光科技股份有限公司");
            var branchNode = nodes.Add("🏬 杭州分公司");
            var researchNode = nodes.Add("🔬 研发中心");

            // === 第二层：部门节点 ===
            // 技术部门
            var techDept = companyNode.Nodes.Add("💻 技术部");
            var salesDept = companyNode.Nodes.Add("📈 销售部");
            var hrDept = companyNode.Nodes.Add("👥 人事部");
            var financeDept = companyNode.Nodes.Add("💰 财务部");

            // 分公司部门
            var branchTech = branchNode.Nodes.Add("💻 技术分部");
            var branchSales = branchNode.Nodes.Add("📈 销售分部");

            // 研发中心部门
            var aiLab = researchNode.Nodes.Add("🤖 AI实验室");
            var iotLab = researchNode.Nodes.Add("🌐 物联网实验室");

            // === 第三层：团队/项目节点 ===
            // 技术部团队
            var frontendTeam = techDept.Nodes.Add("🎨 前端开发团队");
            var backendTeam = techDept.Nodes.Add("⚙️ 后端开发团队");
            var testTeam = techDept.Nodes.Add("🧪 测试团队");
            var devopsTeam = techDept.Nodes.Add("🔧 运维团队");

            // 销售部团队
            var enterpriseSales = salesDept.Nodes.Add("🏭 企业销售团队");
            var channelSales = salesDept.Nodes.Add("🤝 渠道销售团队");
            var customerService = salesDept.Nodes.Add("📞 客户服务团队");

            // 人事部团队
            var recruitment = hrDept.Nodes.Add("🎯 招聘团队");
            var training = hrDept.Nodes.Add("📚 培训团队");

            // AI实验室项目
            var nlpProject = aiLab.Nodes.Add("📝 自然语言处理项目");
            var visionProject = aiLab.Nodes.Add("👁️ 计算机视觉项目");

            // 物联网实验室项目
            var sensorProject = iotLab.Nodes.Add("📡 传感器网络项目");
            var edgeProject = iotLab.Nodes.Add("⚡ 边缘计算项目");

            // === 第四层：员工节点 ===
            // 前端团队成员
            var frontendLead = frontendTeam.Nodes.Add("👨‍💼 张三 - 前端架构师");
            var frontendDev1 = frontendTeam.Nodes.Add("👩‍💻 李四 - 高级前端工程师");
            var frontendDev2 = frontendTeam.Nodes.Add("👨‍💻 王五 - 前端工程师");
            var frontendIntern = frontendTeam.Nodes.Add("🎓 赵六 - 前端实习生");

            // 后端团队成员
            var backendLead = backendTeam.Nodes.Add("👨‍💼 孙七 - 后端架构师");
            var backendDev1 = backendTeam.Nodes.Add("👩‍💻 周八 - 高级后端工程师");
            var backendDev2 = backendTeam.Nodes.Add("👨‍💻 吴九 - 后端工程师");

            // 测试团队成员
            var testLead = testTeam.Nodes.Add("👨‍💼 郑十 - 测试经理");
            var testEngineer1 = testTeam.Nodes.Add("👩‍💻 钱一 - 自动化测试工程师");
            var testEngineer2 = testTeam.Nodes.Add("👨‍💻 陈二 - 功能测试工程师");

            // 销售团队成员
            var salesManager = enterpriseSales.Nodes.Add("👨‍💼 刘三 - 销售经理");
            var salesRep1 = enterpriseSales.Nodes.Add("👩‍💼 林四 - 高级销售代表");
            var salesRep2 = enterpriseSales.Nodes.Add("👨‍💼 黄五 - 销售代表");

            // === 第五层：任务/项目节点 ===
            // 前端架构师的任务
            frontendLead.Nodes.Add("📋 TreeView拖拽控件开发");
            frontendLead.Nodes.Add("📋 UI组件库架构设计");
            frontendLead.Nodes.Add("📋 前端性能优化方案");

            // 高级前端工程师的任务
            frontendDev1.Nodes.Add("📋 用户界面重构");
            frontendDev1.Nodes.Add("📋 响应式布局适配");
            frontendDev1.Nodes.Add("📋 代码审查和指导");

            // 后端架构师的任务
            backendLead.Nodes.Add("📋 微服务架构设计");
            backendLead.Nodes.Add("📋 数据库优化方案");
            backendLead.Nodes.Add("📋 API接口规范制定");

            // 测试经理的任务
            testLead.Nodes.Add("📋 测试计划制定");
            testLead.Nodes.Add("📋 测试流程优化");
            testLead.Nodes.Add("📋 质量标准制定");

            // 自动化测试工程师的任务
            testEngineer1.Nodes.Add("📋 自动化测试框架搭建");
            testEngineer1.Nodes.Add("📋 UI自动化测试脚本");
            testEngineer1.Nodes.Add("📋 接口自动化测试");

            // 销售经理的任务
            salesManager.Nodes.Add("📋 季度销售目标制定");
            salesManager.Nodes.Add("📋 客户关系维护");
            salesManager.Nodes.Add("📋 销售团队培训");

            // === 添加一些特殊状态的节点用于测试 ===
            var specialNodes = nodes.Add("🔧 测试专用节点");
            specialNodes.Nodes.Add("✅ 已完成任务");
            specialNodes.Nodes.Add("⏳ 进行中任务");
            specialNodes.Nodes.Add("❌ 已取消任务");
            specialNodes.Nodes.Add("⚠️ 待确认任务");
            specialNodes.Nodes.Add("🔒 锁定任务");

            // 添加深层嵌套测试节点
            var deepTest = specialNodes.Nodes.Add("📊 深层嵌套测试");
            var level6 = deepTest.Nodes.Add("📁 第6层目录");
            var level7 = level6.Nodes.Add("📁 第7层目录");
            level7.Nodes.Add("📄 深层文件1");
            level7.Nodes.Add("📄 深层文件2");
            level7.Nodes.Add("📄 深层文件3");
        }

        /// <summary>
        /// 拖拽模式下拉框切换事件，动态设置FpiTreeView和FpiUiTreeView的拖拽模式。
        /// 该事件处理程序在用户切换拖拽模式时触发，负责：
        /// 1. 根据下拉框选择的索引确定拖拽模式
        /// 2. 同步设置两个TreeView控件的拖拽模式
        /// 3. 确保两个控件具有相同的拖拽行为以便对比测试
        ///
        /// 拖拽模式说明：
        /// - 索引0：任意层级可拖拽 - 允许节点在任意层级间移动
        /// - 索引1：同层级可拖拽 - 只允许相同层级的节点间移动
        /// - 索引2：同层级同父节点可拖拽 - 只允许相同父节点下的节点间排序
        /// </summary>
        /// <param name="sender">事件源（下拉框控件）</param>
        /// <param name="e">事件参数</param>
        private void ComboBoxDragMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch(comboBoxDragMode.SelectedIndex)
            {
                case 0:
                    // 任意层级拖拽模式
                    fpiTreeView1.DragDropMode = DragDropMode.AnyLevel;
                    uiTreeView1.DragDropMode = FpiDragDropMode.AnyLevel;
                    break;
                case 1:
                    // 同层级拖拽模式
                    fpiTreeView1.DragDropMode = DragDropMode.SameLevel;
                    uiTreeView1.DragDropMode = FpiDragDropMode.SameLevel;
                    break;
                case 2:
                default:
                    // 同层级同父节点拖拽模式（默认）
                    fpiTreeView1.DragDropMode = DragDropMode.SameParentAndLevel;
                    uiTreeView1.DragDropMode = FpiDragDropMode.SameParentAndLevel;
                    break;
            }
        }

        #region 右键菜单事件处理

        /// <summary>
        /// 添加子节点菜单项点击事件
        /// </summary>
        private void AddNodeItem_Click(object sender, EventArgs e)
        {
            var selectedNode = GetSelectedNode();
            if (selectedNode != null)
            {
                var newNodeText = $"新节点_{DateTime.Now:HHmmss}";
                var newNode = selectedNode.Nodes.Add(newNodeText);
                selectedNode.Expand(); // 展开父节点以显示新添加的子节点

                // 同步到另一个TreeView
                SyncNodeOperation("add", selectedNode, newNodeText);
            }
        }

        /// <summary>
        /// 删除节点菜单项点击事件
        /// </summary>
        private void DeleteNodeItem_Click(object sender, EventArgs e)
        {
            var selectedNode = GetSelectedNode();
            if (selectedNode != null && selectedNode.Parent != null) // 不允许删除根节点
            {
                var result = MessageBox.Show($"确定要删除节点 '{selectedNode.Text}' 及其所有子节点吗？",
                    "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var parentNode = selectedNode.Parent;
                    var nodeIndex = selectedNode.Index;
                    selectedNode.Remove();

                    // 同步到另一个TreeView
                    SyncNodeOperation("delete", parentNode, null, nodeIndex);
                }
            }
        }

        /// <summary>
        /// 重命名节点菜单项点击事件
        /// </summary>
        private void RenameNodeItem_Click(object sender, EventArgs e)
        {
            var selectedNode = GetSelectedNode();
            if (selectedNode != null)
            {
                // 使用简单的输入对话框
                string newName = ShowInputDialog("请输入新的节点名称:", "重命名节点", selectedNode.Text);

                if (!string.IsNullOrWhiteSpace(newName) && newName != selectedNode.Text)
                {
                    var oldText = selectedNode.Text;
                    selectedNode.Text = newName;

                    // 同步到另一个TreeView
                    SyncNodeOperation("rename", selectedNode, newName);
                }
            }
        }

        /// <summary>
        /// 显示简单的输入对话框
        /// </summary>
        private string ShowInputDialog(string prompt, string title, string defaultValue = "")
        {
            Form inputForm = new Form()
            {
                Width = 400,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = title,
                StartPosition = FormStartPosition.CenterParent,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label label = new Label() { Left = 20, Top = 20, Width = 350, Text = prompt };
            TextBox textBox = new TextBox() { Left = 20, Top = 50, Width = 250, Text = defaultValue };
            Button okButton = new Button() { Text = "确定", Left = 280, Width = 80, Top = 48, DialogResult = DialogResult.OK };
            Button cancelButton = new Button() { Text = "取消", Left = 280, Width = 80, Top = 78, DialogResult = DialogResult.Cancel };

            okButton.Click += (sender, e) => { inputForm.Close(); };
            cancelButton.Click += (sender, e) => { inputForm.Close(); };

            inputForm.Controls.Add(label);
            inputForm.Controls.Add(textBox);
            inputForm.Controls.Add(okButton);
            inputForm.Controls.Add(cancelButton);
            inputForm.AcceptButton = okButton;
            inputForm.CancelButton = cancelButton;

            return inputForm.ShowDialog() == DialogResult.OK ? textBox.Text : "";
        }

        /// <summary>
        /// 展开所有节点菜单项点击事件
        /// </summary>
        private void ExpandAllItem_Click(object sender, EventArgs e)
        {
            fpiTreeView1.ExpandAll();
            uiTreeView1.ExpandAll();
        }

        /// <summary>
        /// 收起所有节点菜单项点击事件
        /// </summary>
        private void CollapseAllItem_Click(object sender, EventArgs e)
        {
            fpiTreeView1.CollapseAll();
            uiTreeView1.CollapseAll();
        }

        /// <summary>
        /// 刷新数据菜单项点击事件
        /// </summary>
        private void RefreshDataItem_Click(object sender, EventArgs e)
        {
            InitTreeView(fpiTreeView1.Nodes);
            InitTreeView(uiTreeView1.Nodes);
            fpiTreeView1.ExpandAll();
            uiTreeView1.ExpandAll();
        }

        /// <summary>
        /// 获取当前选中的节点（优先从有焦点的TreeView获取）
        /// </summary>
        private TreeNode GetSelectedNode()
        {
            if (fpiTreeView1.Focused && fpiTreeView1.SelectedNode != null)
                return fpiTreeView1.SelectedNode;
            else if (uiTreeView1.InnerTreeView.Focused && uiTreeView1.SelectedNode != null)
                return uiTreeView1.SelectedNode;
            else
                return fpiTreeView1.SelectedNode ?? uiTreeView1.SelectedNode;
        }

        /// <summary>
        /// 同步节点操作到另一个TreeView控件
        /// </summary>
        private void SyncNodeOperation(string operation, TreeNode targetNode, string newText = null, int index = -1)
        {
            try
            {
                // 这里可以实现更复杂的同步逻辑
                // 为了简化演示，暂时不实现完整的同步功能
                // 实际项目中可以根据节点路径或唯一标识来同步操作
            }
            catch (Exception ex)
            {
                MessageBox.Show($"同步操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
