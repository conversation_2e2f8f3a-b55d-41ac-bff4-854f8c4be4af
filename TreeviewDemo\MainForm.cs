﻿using Sunny.UI;
using System;
using System.Windows.Forms;

namespace TreeviewDemo
{
    /// <summary>
    /// 主窗体，演示FpiUiTreeView（Sunny.UI版）与FpiUiTreeViewStandard（标准TreeView版）拖拽排序功能对比
    /// </summary>
    public partial class MainForm : UIForm
    {
        /// <summary>
        /// 构造函数，初始化窗体
        /// </summary>
        public MainForm()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载时，分别为左右两个TreeView控件填充多层级虚拟数据，并初始化拖拽模式下拉框
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            // 填充Sunny.UI自定义TreeView控件
            InitTreeView(uiTreeView1.Nodes);
            uiTreeView1.ExpandAll();
            // 填充标准TreeView自定义控件
            InitTreeView(fpiTreeView1.Nodes);
            fpiTreeView1.ExpandAll();
            // 初始化拖拽模式下拉框
            comboBoxDragMode.SelectedIndex = 2; // 默认同层级同父节点
            comboBoxDragMode.SelectedIndexChanged += ComboBoxDragMode_SelectedIndexChanged;
        }

        /// <summary>
        /// 填充多层级虚拟数据到指定节点集合
        /// </summary>
        /// <param name="nodes">目标节点集合</param>
        private void InitTreeView(TreeNodeCollection nodes)
        {
            nodes.Clear();
            // 一级节点
            var nodeA = nodes.Add("一级节点A");
            var nodeB = nodes.Add("一级节点B");
            var nodeC = nodes.Add("一级节点C");

            // 二级节点
            var nodeA1 = nodeA.Nodes.Add("二级节点A1");
            var nodeA2 = nodeA.Nodes.Add("二级节点A2");
            var nodeB1 = nodeB.Nodes.Add("二级节点B1");
            var nodeB2 = nodeB.Nodes.Add("二级节点B2");
            var nodeC1 = nodeC.Nodes.Add("二级节点C1");

            // 三级节点
            var nodeA1_1 = nodeA1.Nodes.Add("三级节点A1-1");
            var nodeA1_2 = nodeA1.Nodes.Add("三级节点A1-2");
            var nodeA2_1 = nodeA2.Nodes.Add("三级节点A2-1");
            var nodeB1_1 = nodeB1.Nodes.Add("三级节点B1-1");
            var nodeC1_1 = nodeC1.Nodes.Add("三级节点C1-1");
            var nodeC1_2 = nodeC1.Nodes.Add("三级节点C1-2");

            // 四级节点
            nodeA1_1.Nodes.Add("四级节点A1-1-1");
            nodeA1_1.Nodes.Add("四级节点A1-1-2");
            nodeA1_2.Nodes.Add("四级节点A1-2-1");
            nodeB1_1.Nodes.Add("四级节点B1-1-1");
            nodeC1_1.Nodes.Add("四级节点C1-1-1");
            nodeC1_2.Nodes.Add("四级节点C1-2-1");
            nodeC1_2.Nodes.Add("四级节点C1-2-2");
        }

        /// <summary>
        /// 拖拽模式下拉框切换事件，动态设置FpiTreeView和FpiUiTreeView的拖拽模式
        /// </summary>
        private void ComboBoxDragMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch(comboBoxDragMode.SelectedIndex)
            {
                case 0:
                    fpiTreeView1.DragDropMode = DragDropMode.AnyLevel;
                    uiTreeView1.DragDropMode = FpiDragDropMode.AnyLevel;
                    break;
                case 1:
                    fpiTreeView1.DragDropMode = DragDropMode.SameLevel;
                    uiTreeView1.DragDropMode = FpiDragDropMode.SameLevel;
                    break;
                case 2:
                default:
                    fpiTreeView1.DragDropMode = DragDropMode.SameParentAndLevel;
                    uiTreeView1.DragDropMode = FpiDragDropMode.SameParentAndLevel;
                    break;
            }
        }
    }
}
